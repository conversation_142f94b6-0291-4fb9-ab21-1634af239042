# Scripts Directory

This directory contains utility scripts for the Smart Office Assistant application.

## 📁 Available Scripts

### 🔐 User Management Scripts

#### `seed-users.js`
Creates seed users with proper authentication using Supabase Admin API.

**Usage:**
```bash
npm run seed:users
# or
node scripts/seed-users.js
```

**Requirements:**
- `SUPABASE_URL` environment variable
- `SUPABASE_SERVICE_ROLE_KEY` environment variable

**Features:**
- Creates 1 admin user and 4 regular users
- Proper password hashing through Supabase Auth
- Atomic operations with rollback on failure
- Duplicate prevention
- Comprehensive error handling

#### `verify-users.js`
Verifies that seed users were created correctly and can authenticate.

**Usage:**
```bash
npm run verify:users
# or
node scripts/verify-users.js
```

**Requirements:**
- `SUPABASE_URL` environment variable
- `SUPABASE_ANON_KEY` environment variable

**Features:**
- Tests authentication for all seed users
- Verifies database record integrity
- Checks user roles and permissions
- Validates employee details and preferences

## 🔧 Environment Setup

### Option 1: Use .env.seed file
1. Copy `.env.seed.template` to `.env.seed`
2. Fill in your Supabase credentials
3. Run the scripts

### Option 2: Use existing .env file
The scripts will automatically use existing environment variables if available.

### Required Environment Variables

```bash
# For seed-users.js
SUPABASE_URL=your_supabase_project_url
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# For verify-users.js
SUPABASE_URL=your_supabase_project_url
SUPABASE_ANON_KEY=your_anon_key
```

## 🚀 Quick Start

1. **Set up environment variables:**
   ```bash
   cp .env.seed.template .env.seed
   # Edit .env.seed with your Supabase credentials
   ```

2. **Create seed users:**
   ```bash
   npm run seed:users
   ```

3. **Verify users were created:**
   ```bash
   npm run verify:users
   ```

## 📋 Seed User Credentials

After running the seed script, you can test with these credentials:

- **Admin:** <EMAIL> / AdminPass123!
- **HR Manager:** <EMAIL> / UserPass123!
- **Engineer:** <EMAIL> / UserPass123!
- **Marketing:** <EMAIL> / UserPass123!
- **Finance:** <EMAIL> / UserPass123!

## 🔒 Security Notes

- Service role keys have admin privileges - keep them secure
- Never commit `.env.seed` to version control
- Use different keys for development and production
- Change default passwords before production deployment

## 📚 Documentation

For detailed information, see:
- [User Seed Data Documentation](../docs/USER_SEED_DATA.md)
- [Production Implementation Tracker](../PRODUCTION_IMPLEMENTATION_TRACKER.md)
