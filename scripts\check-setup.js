#!/usr/bin/env node

/**
 * Smart Office Assistant - Setup Verification Script
 * 
 * This script checks if all required environment variables are properly configured
 * for running the user seed script.
 */

const fs = require('fs');
require('dotenv').config();
if (fs.existsSync('.env.seed')) {
  require('dotenv').config({ path: '.env.seed' });
}

console.log('🔍 Checking Smart Office Assistant Setup...\n');

// Check required environment variables
const SUPABASE_URL = process.env.SUPABASE_URL || process.env.EXPO_PUBLIC_SUPABASE_URL;
const SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;
const SUPABASE_ANON_KEY = process.env.SUPABASE_ANON_KEY || process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;

let allGood = true;

// Check Supabase URL
if (!SUPABASE_URL) {
  console.log('❌ SUPABASE_URL is missing');
  console.log('   Add EXPO_PUBLIC_SUPABASE_URL to your .env file\n');
  allGood = false;
} else if (SUPABASE_URL.includes('your_supabase') || SUPABASE_URL === 'MISSING_SUPABASE_URL_ENV_VAR') {
  console.log('❌ SUPABASE_URL is not configured properly');
  console.log(`   Current value: ${SUPABASE_URL}`);
  console.log('   Please set a valid Supabase project URL\n');
  allGood = false;
} else {
  console.log('✅ SUPABASE_URL is configured');
  console.log(`   URL: ${SUPABASE_URL}\n`);
}

// Check Service Role Key
if (!SUPABASE_SERVICE_ROLE_KEY) {
  console.log('❌ SUPABASE_SERVICE_ROLE_KEY is missing');
  console.log('   This is required for creating users with the seed script');
  console.log('   Get it from: Supabase Dashboard → Settings → API → service_role key\n');
  allGood = false;
} else if (SUPABASE_SERVICE_ROLE_KEY.includes('REPLACE_WITH') || SUPABASE_SERVICE_ROLE_KEY.length < 100) {
  console.log('❌ SUPABASE_SERVICE_ROLE_KEY is not configured properly');
  console.log('   Current value appears to be a placeholder');
  console.log('   Please replace with your actual service role key from Supabase Dashboard\n');
  allGood = false;
} else {
  console.log('✅ SUPABASE_SERVICE_ROLE_KEY is configured');
  console.log(`   Key: ${SUPABASE_SERVICE_ROLE_KEY.substring(0, 20)}...\n`);
}

// Check Anon Key
if (!SUPABASE_ANON_KEY) {
  console.log('❌ SUPABASE_ANON_KEY is missing');
  console.log('   Add EXPO_PUBLIC_SUPABASE_ANON_KEY to your .env file\n');
  allGood = false;
} else if (SUPABASE_ANON_KEY.includes('your_supabase') || SUPABASE_ANON_KEY === 'MISSING_SUPABASE_ANON_KEY_ENV_VAR') {
  console.log('❌ SUPABASE_ANON_KEY is not configured properly');
  console.log('   Please set a valid Supabase anon key\n');
  allGood = false;
} else {
  console.log('✅ SUPABASE_ANON_KEY is configured');
  console.log(`   Key: ${SUPABASE_ANON_KEY.substring(0, 20)}...\n`);
}

// Check if files exist
const requiredFiles = [
  'scripts/seed-users.js',
  'scripts/verify-users.js',
  'package.json'
];

console.log('📁 Checking required files...');
for (const file of requiredFiles) {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file} exists`);
  } else {
    console.log(`❌ ${file} is missing`);
    allGood = false;
  }
}

console.log('\n' + '='.repeat(50));

if (allGood) {
  console.log('🎉 Setup looks good! You can now run:');
  console.log('   npm run seed:users    # Create seed users');
  console.log('   npm run verify:users  # Verify users can authenticate');
} else {
  console.log('⚠️  Setup issues found. Please fix the issues above and try again.');
  console.log('\n📋 Quick fix checklist:');
  console.log('1. Get your service role key from Supabase Dashboard → Settings → API');
  console.log('2. Update SUPABASE_SERVICE_ROLE_KEY in your .env file');
  console.log('3. Run this script again to verify: node scripts/check-setup.js');
  console.log('4. Once setup is complete, run: npm run seed:users');
}

console.log('\n📚 For detailed instructions, see: scripts/setup-service-key.md');
